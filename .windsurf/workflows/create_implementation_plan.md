---
description: Create a detailed, step-by-step implementation plan for a user's request and save it as a markdown file in the /tasks directory.
---

This workflow outlines the steps for an AI to assist a user in creating a detailed implementation plan for their request. The output will be a markdown file with checkboxes, stored in the `/tasks` directory (relative to project root). **No actual code changes or file modifications (other than the plan itself) should be made when following this workflow.**

**Note on Iteration:** This workflow primarily describes the creation of a *new* plan. However, if the user provides feedback *immediately* after a plan is presented using this workflow, the AI agent should interpret this as a request to **update the plan file just discussed**, rather than creating a completely new one. The agent should use the file path from the previous step for the update.

1.  **Acknowledge and Understand Request**:
    *   Confirm receipt of the user's request to create an implementation plan.
    *   Ask clarifying questions to ensure full understanding of the requirements, scope, and any constraints.

2.  **Break Down the Request**:
    *   Decompose the user's high-level request into smaller, manageable high-level tasks or epics.

3.  **Detail Implementation Steps for Each Task**:
    *   For each high-level task, list the specific, actionable steps required for its implementation. **Exclude any steps related to automated or manual testing, as this will be handled separately by the user.**
    *   **Crucially, all steps should be phrased as actions the implementing AI can perform programmatically or via tool calls. Avoid steps that would require the implementing AI to instruct the *user* to perform manual actions in an editor (e.g., 'User should create a Resource file in Godot and attach it...'). Instead, the step should be something like 'Create a new Godot Resource file programmatically (e.g., `MyResource.tres`) and define its properties via code/scripting. Then, create a new scene and instance a node, attaching the aforementioned resource to an exported property of that node.'**
    *   Consider potential challenges or dependencies for each step.

4.  **Structure the Plan in Markdown**:
    *   Organize the tasks and steps logically (e.g., sequential, by feature component).
    *   Use markdown formatting for clarity (headings, lists, bolding).
    *   Crucially, ensure each actionable item (task or sub-step) is prefixed with a markdown checkbox (`- [ ]`).
    *   **The plan should focus solely on the user's requested changes and their documentation. Do not include meta-steps about the plan file's own creation process or storage (e.g., checking for the `/tasks` directory's existence, as the workflow itself handles this).**
    *   **Ensure the plan includes a dedicated final section or phase for 'Documentation Updates'. This section should prompt for:**
        *   **Updating or creating relevant design documents in the project's `/docs/` directory (relative to project root), ensuring they adhere to the project's `.windsurf/rules/documentation_guidelines.md`.**
        *   **Specifically, if the changes impact game state, ensure documentation related to state is updated or created in `/docs/state/` (relative to project root), also adhering to `.windsurf/rules/documentation_guidelines.md`.**

5.  **Determine Filename, Create Directory (if needed), and Save Location**:
    *   Propose a descriptive filename for the new plan (e.g., `YYYY-MM-DD-feature-description-plan.md` or `request-summary-plan.md`).
    *   The file MUST be saved in the `/tasks` directory, relative to the project root. **If the `/tasks` directory (relative to the project root) does not exist, the AI should create it.** Store the proposed relative path (e.g., `./tasks/proposed-filename.md`) for use in the next step.

6.  **Generate the Full Plan File Content**:
    *   Compile the detailed steps into the complete markdown format for the new plan.

7.  **Present Plan and Instruct Save**:
    *   Inform the user that the plan has been formulated.
    *   Provide the full content of the plan and instruct the user (or another AI agent) to **save it as a new file** to the designated path determined in the previous step (e.g., `./tasks/your-plan-filename.md`).
    *   **CRITICAL**: Reiterate that no code has been written or modified in the project; only this plan file is being created.

8.  **Request Review and Feedback**:
    *   Ask the user to review the generated plan for completeness, correctness, and clarity.
    *   Request feedback. **(Agent Note: If the user provides feedback immediately in the next turn, interpret it as a request to update the plan file at the path just provided in Step 7. Revise the plan content based on feedback and instruct the user/agent to *update* the existing file by replacing its content.)**

**Example Markdown Structure**:

```markdown
# Implementation Plan: [Feature/Request Name]

## Task 1: [High-Level Task Description]
- [ ] Step 1.1: [Detailed action]
- [ ] Step 1.2: [Detailed action]
  - [ ] Sub-step 1.2.1: [Further detail]

## Task 2: [Another High-Level Task Description]
- [ ] Step 2.1: [Detailed action]
- [ ] Step 2.2: [Detailed action]

## Documentation Updates
- [ ] Update or create design documents in `/docs/` (as needed, following `.windsurf/rules/documentation_guidelines.md`)
- [ ] Update or create state documentation in `/docs/state/` (if applicable, following `.windsurf/rules/documentation_guidelines.md`)

## Notes/Considerations:
- [ ] [Any important note or dependency]
