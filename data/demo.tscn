[gd_scene load_steps=5 format=3 uid="uid://sdgew7sm2686"]

[ext_resource type="Texture2D" uid="uid://2cvgmt2fmpao" path="res://data/icon.svg" id="1_c7hdb"]
[ext_resource type="FontFile" uid="uid://c2o4vx2jik43e" path="res://data/Hack-Regular.ttf" id="3_fjiyg"]
[ext_resource type="Script" path="res://src/MyNode.cs" id="3_qvd0t"]
[ext_resource type="ButtonGroup" uid="uid://ucf261yyiwkq" path="res://data/buttongroup.tres" id="4_7oj01"]

[node name="Control" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="MyNode" type="Node" parent="."]
script = ExtResource("3_qvd0t")

[node name="TextureRect" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -128.0
offset_top = -128.0
offset_right = 128.0
offset_bottom = 128.0
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource("1_c7hdb")

[node name="PanelContainer" type="PanelContainer" parent="."]
layout_mode = 1
anchors_preset = 2
anchor_top = 1.0
anchor_bottom = 1.0
offset_top = -143.0
offset_right = 256.0
grow_vertical = 0

[node name="CenterContainer" type="CenterContainer" parent="PanelContainer"]
layout_mode = 2

[node name="VBoxContainer" type="VBoxContainer" parent="PanelContainer/CenterContainer"]
layout_mode = 2

[node name="Label" type="Label" parent="PanelContainer/CenterContainer/VBoxContainer"]
layout_mode = 2
text = "Content scale mode"
horizontal_alignment = 1

[node name="ButtonCIE" type="Button" parent="PanelContainer/CenterContainer/VBoxContainer"]
layout_mode = 2
focus_mode = 0
toggle_mode = true
button_pressed = true
button_group = ExtResource("4_7oj01")
text = "canvas_items/expand"

[node name="ButtonD" type="Button" parent="PanelContainer/CenterContainer/VBoxContainer"]
layout_mode = 2
focus_mode = 0
toggle_mode = true
button_group = ExtResource("4_7oj01")
text = "disabled"

[node name="CanvasLayer" type="CanvasLayer" parent="."]
layer = 120

[node name="TextureRect2" type="TextureRect" parent="CanvasLayer"]
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -64.0
offset_top = -64.0
grow_horizontal = 0
grow_vertical = 0
texture = ExtResource("1_c7hdb")
expand_mode = 1

[node name="CenterContainer" type="CenterContainer" parent="."]
layout_mode = 0
offset_right = 209.0
offset_bottom = 173.0

[node name="VBoxContainer" type="VBoxContainer" parent="CenterContainer"]
custom_minimum_size = Vector2(180, 0)
layout_mode = 2

[node name="Button1" type="Button" parent="CenterContainer/VBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
focus_mode = 0
theme_override_fonts/font = ExtResource("3_fjiyg")
theme_override_font_sizes/font_size = 24
text = "Widgets"

[node name="Button2" type="Button" parent="CenterContainer/VBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
focus_mode = 0
theme_override_fonts/font = ExtResource("3_fjiyg")
theme_override_font_sizes/font_size = 24
text = "New Window"

[node name="Button3" type="Button" parent="CenterContainer/VBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
focus_mode = 0
theme_override_fonts/font = ExtResource("3_fjiyg")
theme_override_font_sizes/font_size = 24
text = "3D Scene"

[node name="CheckBox" type="CheckBox" parent="CenterContainer/VBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
focus_mode = 0
text = "ImGui Viewports"

[connection signal="pressed" from="PanelContainer/CenterContainer/VBoxContainer/ButtonCIE" to="MyNode" method="OnContentScaleCIE"]
[connection signal="pressed" from="PanelContainer/CenterContainer/VBoxContainer/ButtonD" to="MyNode" method="OnContentScaleDisabled"]
