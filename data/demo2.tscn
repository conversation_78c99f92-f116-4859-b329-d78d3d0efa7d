[gd_scene load_steps=7 format=3 uid="uid://dr8hy6iu5hk8a"]

[ext_resource type="Script" path="res://src/ViewportArea.cs" id="2_btjlu"]
[ext_resource type="Script" path="res://src/MySecondNode.cs" id="2_gnjei"]

[sub_resource type="PlaneMesh" id="PlaneMesh_dsg5q"]
size = Vector2(250, 250)

[sub_resource type="CylinderMesh" id="CylinderMesh_kek8d"]
top_radius = 2.0
bottom_radius = 10.0
height = 30.0

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_eanfn"]
albedo_color = Color(0.615686, 0.556863, 0.968627, 1)

[sub_resource type="BoxShape3D" id="BoxShape3D_6s853"]
size = Vector3(230, 1, 230)

[node name="Background" type="ColorRect"]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
color = Color(0.0784314, 0, 0.117647, 1)

[node name="MySecondNode" type="Node" parent="."]
script = ExtResource("2_gnjei")

[node name="ShowHideButton" type="Button" parent="."]
unique_name_in_owner = true
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -96.0
offset_top = -48.0
offset_right = -7.0
offset_bottom = -8.0
grow_horizontal = 0
grow_vertical = 0
focus_mode = 0
toggle_mode = true
text = "hide"

[node name="SubViewport" type="SubViewport" parent="."]
unique_name_in_owner = true
physics_object_picking = true
render_target_update_mode = 4

[node name="Board" type="MeshInstance3D" parent="SubViewport"]
unique_name_in_owner = true
mesh = SubResource("PlaneMesh_dsg5q")

[node name="Piece" type="MeshInstance3D" parent="SubViewport"]
unique_name_in_owner = true
layers = 2
mesh = SubResource("CylinderMesh_kek8d")
surface_material_override/0 = SubResource("StandardMaterial3D_eanfn")

[node name="Camera3d" type="Camera3D" parent="SubViewport"]
transform = Transform3D(1, 0, 0, 0, 0.422618, 0.906308, 0, -0.906308, 0.422618, 0, 150, 100)
current = true

[node name="Area3d" type="Area3D" parent="SubViewport"]
script = ExtResource("2_btjlu")

[node name="CollisionShape3d" type="CollisionShape3D" parent="SubViewport/Area3d"]
shape = SubResource("BoxShape3D_6s853")
