[gd_scene load_steps=12 format=3 uid="uid://0iek6ri88gkb"]

[ext_resource type="PackedScene" uid="uid://dvl383ispakmn" path="res://data/gui_panel_3d.tscn" id="1"]
[ext_resource type="Script" path="res://src/ThirdNode.cs" id="2_xsiun"]

[sub_resource type="ProceduralSkyMaterial" id="ProceduralSkyMaterial_1lgdv"]
sky_horizon_color = Color(0.64625, 0.65575, 0.67075, 1)
ground_horizon_color = Color(0.64625, 0.65575, 0.67075, 1)

[sub_resource type="Sky" id="Sky_pq42j"]
sky_material = SubResource("ProceduralSkyMaterial_1lgdv")

[sub_resource type="Environment" id="Environment_niyks"]
background_mode = 2
sky = SubResource("Sky_pq42j")
tonemap_mode = 2
tonemap_white = 2.0

[sub_resource type="Animation" id="1"]
length = 6.0
loop_mode = 1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Camera3D:transform")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 2, 4, 6),
"transitions": PackedFloat32Array(-2, -2, -2, -2),
"update": 0,
"values": [Transform3D(0.994592, 0, 0.103856, 0, 1, 0, -0.103856, 0, 0.994592, 0.465682, 0, 1.78523), Transform3D(0.962984, 0, -0.269557, 0, 1, 0, 0.269557, 0, 0.962984, -0.462237, 0, 2.41934), Transform3D(0.806599, 0, -0.591098, 0, 1, 0, 0.591098, 0, 0.806599, -1.59502, 0, 2.05358), Transform3D(0.994592, 0, 0.103856, 0, 1, 0, -0.103856, 0, 0.994592, 0.465682, 0, 1.78523)]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_uw4n0"]
_data = {
"Move_camera": SubResource("1")
}

[sub_resource type="PlaneMesh" id="2"]

[sub_resource type="BoxMesh" id="3"]

[sub_resource type="StandardMaterial3D" id="4"]
albedo_color = Color(0.722656, 0.791992, 1, 1)
roughness = 0.0

[sub_resource type="SphereMesh" id="SphereMesh_ecs3k"]

[node name="GUIin3D" type="Node3D"]

[node name="DirectionalLight3D" type="DirectionalLight3D" parent="."]
transform = Transform3D(-0.866023, -0.433016, 0.250001, 0, 0.499998, 0.866027, -0.500003, 0.749999, -0.43301, 0, 0, 0)
visible = false
shadow_enabled = true

[node name="WorldEnvironment" type="WorldEnvironment" parent="."]
environment = SubResource("Environment_niyks")

[node name="GUIPanel3D" parent="." instance=ExtResource("1")]

[node name="Camera3D" type="Camera3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 0.999999, 0, 0, 3)
fov = 74.0
near = 0.1

[node name="OmniLight3D" type="OmniLight3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1.38866, 1.2413, 2.72141)
shadow_enabled = true
shadow_blur = 3.0
omni_range = 10.0

[node name="Camera_Move" type="AnimationPlayer" parent="."]
libraries = {
"": SubResource("AnimationLibrary_uw4n0")
}
autoplay = "Move_camera"

[node name="Background" type="Node3D" parent="."]

[node name="Wall" type="MeshInstance3D" parent="Background"]
transform = Transform3D(4, 0, 0, 0, -1.74846e-07, -4, 0, 4, -1.74846e-07, -2.60819, 0.589247, -2.08943)
mesh = SubResource("2")

[node name="Wall2" type="MeshInstance3D" parent="Background"]
transform = Transform3D(4, 0, 0, 0, -1.74846e-07, -4, 0, 4, -1.74846e-07, 5.08055, 0.589247, -2.08943)
mesh = SubResource("2")

[node name="Wall3" type="MeshInstance3D" parent="Background"]
transform = Transform3D(-1.74846e-07, -4, 0, -1.74846e-07, 7.64274e-15, -4, 4, -1.74846e-07, -1.74846e-07, 9.04446, 0.589247, 1.62058)
mesh = SubResource("2")

[node name="Floor" type="MeshInstance3D" parent="Background"]
transform = Transform3D(4, 0, 0, 0, 4, 0, 0, 0, 4, -2.60819, -2.68765, 1.46502)
mesh = SubResource("2")

[node name="Floor2" type="MeshInstance3D" parent="Background"]
transform = Transform3D(4, 0, 0, 0, 4, 0, 0, 0, 4, 5.08055, -2.68765, 1.46502)
mesh = SubResource("2")

[node name="Cube" type="MeshInstance3D" parent="Background"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 4.25901, -0.598608, 0.374871)
mesh = SubResource("3")
surface_material_override/0 = SubResource("4")

[node name="Sphere" type="MeshInstance3D" parent="Background"]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 2.88761, 2.01326, 0.374871)
mesh = SubResource("SphereMesh_ecs3k")
surface_material_override/0 = SubResource("4")

[node name="ThirdNode" type="Node" parent="."]
script = ExtResource("2_xsiun")
