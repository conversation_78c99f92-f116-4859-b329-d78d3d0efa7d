# Implementation Plan: GTA2-Style 2D Car Controller

This plan outlines the steps to create a 2D arcade-style car controller with a feel similar to Grand Theft Auto 2.

## Phase 1: Core Car Mechanics

### Task 1: Basic Car Scene Setup
- [ ] Create a new Godot scene named `Car.tscn`.
- [ ] Set the root node of `Car.tscn` to be a `RigidBody2D`.
    - [ ] Configure `RigidBody2D` properties (mass, gravity scale (0 for top-down if not using for effects), friction, etc.) as a starting point.
- [ ] Add a `Sprite2D` node as a child to the `RigidBody2D` for the car's visual.
    - [ ] Assign a placeholder car texture to the `Sprite2D`.
- [ ] Add a `CollisionShape2D` node as a child to the `RigidBody2D`.
    - [ ] Assign a `RectangleShape2D` (or other appropriate shape) to the `CollisionShape2D` to define the car's physics body.

### Task 2: Car Controller Script (Initial Setup)
- [ ] Create a new C# script named `CarController.cs`.
- [ ] Attach `CarController.cs` to the `RigidBody2D` node in `Car.tscn`.
- [ ] In `CarController.cs`, declare the class as `partial` and ensure it inherits from `RigidBody2D`.
- [ ] Define exported C# properties in `CarController.cs` for tuning:
    - [ ] `[Export] public float EngineForce { get; set; } = 1000.0f;`
    - [ ] `[Export] public float BrakeForce { get; set; } = 600.0f;`
    - [ ] `[Export] public float MaxSpeed { get; set; } = 800.0f;`
    - [ ] `[Export] public float SteeringRate { get; set; } = 3.5f; // Radians per second, or could be torque value`
    - [ ] `[Export] public float TurnStopDamp { get; set; } = 10.0f; // Damping factor for stopping rotation`
    - [ ] `[Export] public float BaseDrag { get; set; } = 0.5f; // Constant drag/friction coefficient for RigidBody2D.LinearDamp`
    - [ ] `[Export] public float HandbrakeDragFactor { get; set; } = 3.0f; // Multiplier for drag when handbrake is on`
- [ ] Implement the `_PhysicsProcess(double delta)` method in `CarController.cs`.
- [ ] Cache node references (like `Sprite2D` if needed for visual effects tied to controls) in `_Ready()`.

### Task 3: Implement Acceleration and Braking
- [ ] In `CarController.cs` (`_PhysicsProcess`):
    - [ ] Get `accelerate` input action state using `Input.GetActionStrength("accelerate")` or `Input.IsActionPressed("accelerate")`.
    - [ ] If `accelerate` is active:
        - [ ] Calculate forward direction: `Vector2 forwardDirection = Vector2.Up.Rotated(Rotation);` (Assuming car sprite faces upwards).
        - [ ] Apply forward force: `ApplyCentralForce(forwardDirection * EngineForce);`
    - [ ] Get `brake` input action state.
    - [ ] If `brake` is active:
        - [ ] Calculate backward direction: `Vector2 backwardDirection = Vector2.Down.Rotated(Rotation);`
        - [ ] Apply braking force: `ApplyCentralForce(backwardDirection * BrakeForce);`
    - [ ] Apply base linear damping (drag): `LinearDamp = BaseDrag;` (This will be adjusted by handbrake later).
    - [ ] Cap speed:
        - [ ] `if (LinearVelocity.LengthSquared() > MaxSpeed * MaxSpeed) { LinearVelocity = LinearVelocity.Normalized() * MaxSpeed; }`

### Task 4: Implement Steering
- [ ] In `CarController.cs` (`_PhysicsProcess`):
    - [ ] Get `steer_left` and `steer_right` input action states.
    - [ ] Initialize `float steerInput = Input.GetActionStrength("steer_right") - Input.GetActionStrength("steer_left");`
    - [ ] If `steerInput != 0.0f` (steering is active):
        - [ ] Apply steering: `AngularVelocity = steerInput * SteeringRate;`
    - [ ] If `steerInput == 0.0f` (no steering input):
        - [ ] Dampen rotation: `AngularVelocity = Mathf.Lerp(AngularVelocity, 0f, TurnStopDamp * (float)delta);`

### Task 5: Implement Handbrake
- [ ] In `CarController.cs` (`_PhysicsProcess`):
    - [ ] Get `handbrake` input action state.
    - [ ] If `handbrake` is pressed:
        - [ ] Increase linear damping: `LinearDamp = BaseDrag * HandbrakeDragFactor;`
        - [ ] (Optional: To enhance sliding, you might also want to temporarily reduce `AngularDamp` or apply a sideways force based on current velocity and orientation, but start with increased linear drag.)
    - [ ] Else (handbrake not pressed and `LinearDamp` was modified by handbrake):
        - [ ] Revert to normal linear damping: `LinearDamp = BaseDrag;`
        - *Ensure `LinearDamp` is set correctly in one place based on handbrake status and base drag.* A better structure:
          ```csharp
          // In _PhysicsProcess, after handling inputs for forces:
          bool handbrakeActive = Input.IsActionPressed("handbrake");
          if (handbrakeActive)
          {
              LinearDamp = BaseDrag * HandbrakeDragFactor;
              // Optionally adjust AngularDamp for more slide: AngularDamp = SomeLowerValue;
          }
          else
          {
              LinearDamp = BaseDrag;
              // Optionally revert AngularDamp: AngularDamp = DefaultAngularDampValue;
          }
          ```
          (Requires `DefaultAngularDampValue` to be defined, or set `AngularDamp` on the `RigidBody2D` node directly in the editor and only modify it for handbrake if needed).

## Phase 2: Scene Integration and Input

### Task 6: Input Action Setup
- [ ] Open Project > Project Settings > Input Map.
- [ ] Add new actions:
    - [ ] `accelerate`
    - [ ] `brake`
    - [ ] `steer_left`
    - [ ] `steer_right`
    - [ ] `handbrake`
- [ ] Assign default keyboard keys to these actions:
    - [ ] `accelerate`: `W` key and `Up Arrow` key.
    - [ ] `brake`: `S` key and `Down Arrow` key.
    - [ ] `steer_left`: `A` key and `Left Arrow` key.
    - [ ] `steer_right`: `D` key and `Right Arrow` key.
    - [ ] `handbrake`: `Space` key.

### Task 7: Camera Setup
- [ ] Create a new main scene for testing (e.g., `TestLevel.tscn`) or use an existing one.
- [ ] Instance `Car.tscn` into `TestLevel.tscn`.
- [ ] Add a `Camera2D` node to `TestLevel.tscn`.
- [ ] Set `Camera2D.Current = true`.
- [ ] Create a C# script named `FollowCamera.cs` and attach it to the `Camera2D` node.
    - [ ] In `FollowCamera.cs`, declare `public partial class FollowCamera : Camera2D`.
    - [ ] Add `[Export] public NodePath TargetNodePath { get; set; }`
    - [ ] Declare `private Node2D _target;`
    - [ ] In `_Ready()`: `if (TargetNodePath != null) _target = GetNode<Node2D>(TargetNodePath);`
    - [ ] In `_Process(double delta)`: `if (_target != null) GlobalPosition = _target.GlobalPosition;`
    - [ ] In the Godot editor, assign the instanced Car node to the `TargetNodePath` property of the Camera's script.
- [ ] Adjust `Camera2D` properties like `Zoom` (e.g., `Vector2(0.5f, 0.5f)` for a more zoomed-out view), `Smoothing Enabled`, `Smoothing Speed` as needed for the GTA2 top-down feel.

## Phase 3: Refinement and Iteration

### Task 8: Gameplay Tuning and Feel
- [ ] Playtest the car controller extensively in `TestLevel.tscn`.
- [ ] Adjust all exported variables in `CarController.cs` (`EngineForce`, `BrakeForce`, `MaxSpeed`, `SteeringRate`, `BaseDrag`, `HandbrakeDragFactor`, etc.) iteratively.
- [ ] Also tune `RigidBody2D` properties directly in the editor: `Mass`, `PhysicsMaterialOverride` (for `Friction`, `Bounce`), `LinearDamp`, `AngularDamp` (if not fully controlled by script).
- [ ] Pay attention to:
    - [ ] How quickly the car accelerates and reaches top speed.
    - [ ] How quickly it stops when braking.
    - [ ] Responsiveness and turn radius of steering at various speeds.
    - [ ] The nature of slides/drifts with handbrake and sharp turns (GTA2 cars could pivot quite sharply).
    - [ ] How the car interacts with other physics objects if you add them for testing.
- [ ] Consider adding a simple test environment in `TestLevel.tscn` with some obstacles (`StaticBody2D` nodes with `CollisionShape2D`) to test collisions and handling.

## Documentation Updates
- [ ] Update or create design documents in `/docs/` (as needed, following `.windsurf/rules/documentation_guidelines.md`) to reflect the car controller design, key parameters, and intended feel.
- [ ] If the car controller introduces or modifies any shared game state accessible by other systems (e.g., player's current vehicle, speed for UI display), update or create relevant state documentation in `/docs/state/` (following `.windsurf/rules/documentation_guidelines.md`).

## Notes/Considerations:
- [ ] The car sprite should ideally be oriented with its "front" pointing towards the top of the texture (positive Y is "up" in Godot's 2D coordinate system, which `Vector2.Up` represents). If your sprite is oriented differently (e.g., facing right), adjust the `forwardDirection` calculation (e.g., `Vector2.Right.Rotated(Rotation)`).
- [ ] `RigidBody2D` is chosen for its built-in physics. A `CharacterBody2D` (formerly KinematicBody2D) approach would require manual implementation of movement, collision response, and physics-like behaviors (like sliding), offering more precise control but significantly more complex code.
- [ ] Future enhancements not covered: sound effects (engine, tires, collision), particle effects (tire smoke, sparks), damage model, varying vehicle stats.