# Implementation Plan: Zombie Enemies (2D Focus)

## Task 1: Create Zombie Character Entity
- [ ] Create a new C# script named `Zombie.cs` that inherits from `CharacterBody2D`.
    - [ ] Define properties within `Zombie.cs`: `float Health`, `float MaxHealth`, `float MovementSpeed`.
    - [ ] Implement a public parameterless constructor (required for Godot C# script instantiation).
- [ ] Create a new Godot scene `Zombie.tscn`.
    - [ ] Set the root node of `Zombie.tscn` to `CharacterBody2D`.
    - [ ] Add a `Sprite2D` node to the scene for the zombie's visual representation. Assign a placeholder texture.
    - [ ] Add a `CollisionShape2D` node to the scene and configure its shape for physics interactions.
    - [ ] Attach the `Zombie.cs` script to the root node of `Zombie.tscn`.
- [ ] Implement basic zombie behavior in `Zombie.cs` (e.g., in `_PhysicsProcess`):
    - [ ] For now, this can be simple (e.g., standing still, or moving in a predefined way). Advanced AI (like chasing the player) can be a future enhancement.

## Task 2: Implement Car-Zombie Collision and Damage Logic
- [ ] In the existing `Car.cs` script (or equivalent player vehicle script):
    - [ ] Ensure the car has a way to detect collisions with zombies (e.g., using an `Area2D` with a `CollisionShape2D` that detects bodies/areas on a specific "enemy" collision layer, or by checking collision body types if the car is a `RigidBody2D` and handles collisions in `_physics_process` or via signals like `body_entered`).
    - [ ] Define an `[Export] public float ZombieDamageAmount { get; set; } = 25f;` property (or similar) to specify how much damage the car deals to zombies.
- [ ] In `Zombie.cs`:
    - [ ] Add a public method `public void TakeDamage(float amount)`:
        - [ ] This method should reduce the zombie's `Health` by `amount`.
        - [ ] Clamp `Health` so it doesn't go below 0.
        - [ ] Check if `Health <= 0`. If true, call a `Die()` method.
    - [ ] Add a `[Signal] public delegate void DiedEventHandler(Vector2 position);` signal.
    - [ ] Add a private method `private void Die()`:
        - [ ] Emit the `Died` signal, passing the zombie's global position.
        - [ ] Call `QueueFree()` to remove the zombie from the scene.
- [ ] In `Car.cs` (or wherever collision is handled):
    - [ ] When a collision with an entity identified as a zombie occurs, get a reference to its `Zombie.cs` script instance.
    - [ ] Call the `zombieInstance.TakeDamage(ZombieDamageAmount)` method.

## Task 3: Implement Zombie Death Visual Feedback (Blood Splatter)
- [ ] Create a new Godot scene `BloodSplatter.tscn`.
    - [ ] The root node can be `Node2D`.
    - [ ] Add a `Sprite2D` for the visual. Use a placeholder texture for now.
    - [ ] Optionally, add a `Timer` node to control the duration the splatter is visible.
- [ ] Create a C# script `BloodSplatter.cs` and attach it to the root of `BloodSplatter.tscn`.
    - [ ] In `_Ready()`, if using a Timer, start it.
    - [ ] When the timer times out (or after a set duration managed in `_Process`), call `QueueFree()` on the blood splatter instance.
- [ ] In a script that manages game elements or zombie spawning (e.g., `GameManager.cs` or the scene script where zombies are instanced):
    - [ ] When a zombie instance is created, connect to its `Died` signal.
    - [ ] In the handler method for the `Died` signal:
        - [ ] Load the `BloodSplatter.tscn` scene (`ResourceLoader.Load<PackedScene>("res://path/to/BloodSplatter.tscn")`).
        - [ ] Instantiate the blood splatter scene at the position received from the `Died` signal.
        - [ ] Add the instantiated splatter as a child to an appropriate node in the scene tree (e.g., a "Decals" or "Effects" node).

## Task 4: Implement UI Kill Counter
- [ ] Identify or create a UI scene (e.g., `HUD.tscn`) and its corresponding C# script (e.g., `HUD.cs`).
    - [ ] Add a `Label` node to `HUD.tscn` to display the kill count (e.g., "Kills: 0").
- [ ] Create or use an existing global singleton script for game state (e.g., `GameState.cs`). This script should not be a Node, but a plain C# class with static members or a Godot Autoload singleton.
    - [ ] Add `public static int ZombiesKilledCount { get; private set; } = 0;`
    - [ ] Add `public static event Action<int> OnZombiesKilledCountChanged;`
    - [ ] Add a public static method `public static void IncrementKills()`:
        - [ ] `ZombiesKilledCount++;`
        - [ ] `OnZombiesKilledCountChanged?.Invoke(ZombiesKilledCount);`
- [ ] In the script handling the zombie's `Died` signal (from Task 3):
    - [ ] After instantiating the blood splatter, call `GameState.IncrementKills()`.
- [ ] In `HUD.cs`:
    - [ ] Get a reference to the kill count `Label` node (e.g., using `GetNode<Label>("PathToLabel")` in `_Ready()`).
    - [ ] In `_Ready()`, subscribe to the `GameState.OnZombiesKilledCountChanged` event: `GameState.OnZombiesKilledCountChanged += UpdateKillCounterText;`.
    - [ ] Implement the `private void UpdateKillCounterText(int newKillCount)` method:
        - [ ] Update the `Label`'s text: `killCountLabel.Text = $"Kills: {newKillCount}";`.
    - [ ] Remember to unsubscribe from the event in `_ExitTree()` or when the HUD is destroyed to prevent memory leaks: `GameState.OnZombiesKilledCountChanged -= UpdateKillCounterText;`.
    - [ ] Initialize the label text in `_Ready()`: `UpdateKillCounterText(GameState.ZombiesKilledCount);`

## Task 5: Implement Car Slowdown on Zombie Hit
- [ ] In `Car.cs` (or equivalent player vehicle script):
    - [ ] Define an `[Export] public float SlowdownFactorOnHit { get; set; } = 0.8f;` (value between 0 and 1, where 1 means no slowdown).
    - [ ] Define an `[Export] public float MinSpeedAfterHit { get; set; } = 2.0f;` (to ensure car doesn't stop completely).
    - [ ] When a collision with a zombie is detected and damage is applied (from Task 2):
        - [ ] Reduce the car's current velocity. For example, if `velocity` is a `Vector2` representing current speed:
          `currentVelocity *= SlowdownFactorOnHit;`
        - [ ] Ensure the car's speed doesn't drop below a minimum threshold if it's moving forward:
          `if (currentVelocity.Length() < MinSpeedAfterHit && currentVelocity.Length() > 0.1f) { currentVelocity = currentVelocity.Normalized() * MinSpeedAfterHit; }`
          (Adjust logic based on how your car's movement is implemented).

## Documentation Updates
- [ ] Update or create design documents in `/docs/` to reflect:
    - [ ] The new Zombie enemy type, its characteristics, and behaviors (2D specific).
    - [ ] The car's interaction with zombies (collision, damage, slowdown - 2D specific).
    - [ ] The blood splatter effect (2D specific).
    - [ ] The UI kill counter.
    - [ ] Ensure all documentation adheres to the guidelines in `.windsurf/rules/documentation_guidelines.md`.
- [ ] Update or create state documentation in `/docs/state/` for:
    - [ ] The `GameState.ZombiesKilledCount` variable and its associated event.
    - [ ] Ensure this documentation adheres to the guidelines in `.windsurf/rules/documentation_guidelines.md`.

## Notes/Considerations:
- [ ] Ensure collision layers and masks are set up correctly for car-zombie interactions in 2D.
- [ ] The blood splatter is a placeholder; a more advanced 2D particle effect or sprite-based animation can be implemented later.
- [ ] Zombie spawning logic is not covered in this plan but will be necessary to populate the game world with zombies.