[gd_scene load_steps=3 format=3 uid="uid://dqr8xlfvqik2r"]

[ext_resource type="Script" path="res://Game/Src/UI/HUD.cs" id="1_hud"]

[sub_resource type="LabelSettings" id="LabelSettings_1"]
font_size = 24
font_color = Color(1, 1, 1, 1)
outline_size = 2
outline_color = Color(0, 0, 0, 1)

[node name="HUD" type="CanvasLayer"]
script = ExtResource("1_hud")

[node name="Control" type="Control" parent="."]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
mouse_filter = 2

[node name="KillCountLabel" type="Label" parent="Control"]
layout_mode = 0
anchors_preset = 2
anchor_top = 1.0
anchor_bottom = 1.0
offset_left = 20.0
offset_top = -50.0
offset_right = 200.0
offset_bottom = -20.0
text = "Kills: 0"
label_settings = SubResource("LabelSettings_1")
