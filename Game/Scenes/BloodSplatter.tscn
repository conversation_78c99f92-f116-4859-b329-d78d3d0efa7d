[gd_scene load_steps=3 format=3 uid="uid://cqr8xlfvqik2r"]

[ext_resource type="Script" uid="uid://emxktrmqce0x" path="res://Game/Src/BloodSplatter.cs" id="1_blood"]

[sub_resource type="PlaceholderTexture2D" id="PlaceholderTexture2D_1"]
size = Vector2(48, 48)

[node name="BloodSplatter" type="Node2D"]
script = ExtResource("1_blood")

[node name="Sprite2D" type="Sprite2D" parent="."]
modulate = Color(0.8, 0.1, 0.1, 0.8)
texture = SubResource("PlaceholderTexture2D_1")
