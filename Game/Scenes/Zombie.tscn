[gd_scene load_steps=4 format=3 uid="uid://bqr8xlfvqik2r"]

[ext_resource type="Script" path="res://Game/Src/Zombie.cs" id="1_zombie"]

[sub_resource type="PlaceholderTexture2D" id="PlaceholderTexture2D_1"]
size = Vector2(32, 32)

[sub_resource type="RectangleShape2D" id="RectangleShape2D_1"]
size = Vector2(32, 32)

[node name="Zombie" type="CharacterBody2D"]
z_index = -1
collision_layer = 4
collision_mask = 0
script = ExtResource("1_zombie")

[node name="Sprite2D" type="Sprite2D" parent="."]
modulate = Color(0.8, 0.2, 0.2, 1)
texture = SubResource("PlaceholderTexture2D_1")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("RectangleShape2D_1")
