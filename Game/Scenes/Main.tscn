[gd_scene load_steps=7 format=3 uid="uid://dcic6xm7d5f6r"]

[ext_resource type="Script" uid="uid://dgtdysdml11nf" path="res://Game/Src/Main.cs" id="1_mgsyk"]
[ext_resource type="PackedScene" uid="uid://bh7hjbodi6h3b" path="res://Game/Scenes/Game.tscn" id="2_w1egk"]
[ext_resource type="PackedScene" uid="uid://hrcsasqiei0b" path="res://Game/Scenes/ResolutionHelper.tscn" id="3_hh2mx"]
[ext_resource type="PackedScene" uid="uid://b8ixwmhkuqcbm" path="res://Game/Scenes/UI/Settings.tscn" id="4_rchnd"]
[ext_resource type="PackedScene" uid="uid://bvh8kfr5xq4gp" path="res://Game/Scenes/UI/MainMenu.tscn" id="4_rl4yr"]
[ext_resource type="PackedScene" uid="uid://c2g8uyqxjnvvp" path="res://Game/Scenes/UI/PauseMenu.tscn" id="6_yw2oc"]

[node name="Main" type="Control" node_paths=PackedStringArray("ResolutionHelper")]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_mgsyk")
ResolutionHelper = NodePath("ResolutionHelper")

[node name="GameViewportContainer" type="SubViewportContainer" parent="."]
unique_name_in_owner = true
visible = false
texture_filter = 1
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="GameViewport" type="SubViewport" parent="GameViewportContainer"]
unique_name_in_owner = true
disable_3d = true
handle_input_locally = false
canvas_item_default_texture_filter = 0
size = Vector2i(1280, 720)
render_target_update_mode = 0

[node name="Game" parent="GameViewportContainer/GameViewport" instance=ExtResource("2_w1egk")]

[node name="ResolutionHelper" parent="." node_paths=PackedStringArray("ViewportContainer", "Viewport", "GameRootNode") instance=ExtResource("3_hh2mx")]
unique_name_in_owner = true
ViewportContainer = NodePath("../GameViewportContainer")
Viewport = NodePath("../GameViewportContainer/GameViewport")
GameRootNode = NodePath("../GameViewportContainer/GameViewport/Game")

[node name="UI" type="CanvasLayer" parent="."]

[node name="MainMenu" parent="UI" instance=ExtResource("4_rl4yr")]
unique_name_in_owner = true

[node name="Settings" parent="UI" node_paths=PackedStringArray("_resolutionHelper") instance=ExtResource("4_rchnd")]
unique_name_in_owner = true
visible = false
_resolutionHelper = NodePath("../../ResolutionHelper")

[node name="PauseMenu" parent="UI" instance=ExtResource("6_yw2oc")]
unique_name_in_owner = true
visible = false

[editable path="GameViewportContainer/GameViewport/Game"]
[editable path="GameViewportContainer/GameViewport/Game/TestLevel"]
[editable path="UI/MainMenu"]
