[gd_scene load_steps=9 format=3 uid="uid://c7cu6jgujp2k8"]

[ext_resource type="PackedScene" uid="uid://dj0cpgkssya6y" path="res://Game/Scenes/Car.tscn" id="1_car123"]
[ext_resource type="Script" uid="uid://d3d0riko82n6" path="res://Game/Src/FollowCamera.cs" id="2_camera456"]
[ext_resource type="Script" uid="uid://ywot1ejwu8c5" path="res://Game/Src/GameManager.cs" id="3_gamemanager"]
[ext_resource type="PackedScene" uid="uid://dqr8xlfvqik2r" path="res://Game/Scenes/UI/HUD.tscn" id="4_hud"]
[ext_resource type="PackedScene" uid="uid://cqr8xlfvqik2r" path="res://Game/Scenes/BloodSplatter.tscn" id="5_blood"]
[ext_resource type="PackedScene" uid="uid://bqr8xlfvqik2r" path="res://Game/Scenes/Zombie.tscn" id="6_zombie"]
[ext_resource type="Script" uid="uid://b771n2brp5fq3" path="res://Game/Src/TestLevel.cs" id="7_testlevel"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_1"]
size = Vector2(100, 20)

[node name="TestLevel" type="Node2D"]
script = ExtResource("7_testlevel")

[node name="Car" parent="." instance=ExtResource("1_car123")]
position = Vector2(640, 360)
EngineForce = 1000.0
TireTrackFadeDuration = 3.0

[node name="Camera2D" type="Camera2D" parent="." node_paths=PackedStringArray("Target")]
script = ExtResource("2_camera456")
Target = NodePath("../Car")

[node name="GameManager" type="Node2D" parent="."]
script = ExtResource("3_gamemanager")
BloodSplatterScene = ExtResource("5_blood")
ZombieScene = ExtResource("6_zombie")

[node name="HUD" parent="." instance=ExtResource("4_hud")]
grow_horizontal = 2
grow_vertical = 2

[node name="Obstacles" type="Node2D" parent="."]

[node name="Obstacle1" type="StaticBody2D" parent="Obstacles"]
position = Vector2(400, 200)

[node name="CollisionShape2D" type="CollisionShape2D" parent="Obstacles/Obstacle1"]
shape = SubResource("RectangleShape2D_1")

[node name="ColorRect" type="ColorRect" parent="Obstacles/Obstacle1"]
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -50.0
offset_top = -10.0
offset_right = 50.0
offset_bottom = 10.0
grow_horizontal = 2
grow_vertical = 2
color = Color(0.8, 0.2, 0.2, 1)

[node name="Obstacle2" type="StaticBody2D" parent="Obstacles"]
position = Vector2(800, 500)

[node name="CollisionShape2D" type="CollisionShape2D" parent="Obstacles/Obstacle2"]
shape = SubResource("RectangleShape2D_1")

[node name="ColorRect" type="ColorRect" parent="Obstacles/Obstacle2"]
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -50.0
offset_top = -10.0
offset_right = 50.0
offset_bottom = 10.0
grow_horizontal = 2
grow_vertical = 2
color = Color(0.2, 0.8, 0.2, 1)
