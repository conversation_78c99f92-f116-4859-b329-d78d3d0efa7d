using Godot;

namespace CarGame;

public partial class BloodSplatter : Node2D
{
    [Export] public float Duration { get; set; } = 3.0f;
    
    private Timer? _timer;
    
    public override void _Ready()
    {
        // Create and configure timer
        _timer = new Timer();
        _timer.WaitTime = Duration;
        _timer.OneShot = true;
        _timer.Timeout += OnTimeout;
        AddChild(_timer);
        
        // Start the timer
        _timer.Start();
    }
    
    private void OnTimeout()
    {
        // Remove the blood splatter after duration
        QueueFree();
    }
    
    public override void _ExitTree()
    {
        // Clean up timer connection
        if (_timer != null)
        {
            _timer.Timeout -= OnTimeout;
        }
    }
}
