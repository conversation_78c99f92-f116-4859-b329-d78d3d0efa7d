using Godot;

namespace CarGame;

public partial class CarController : RigidBody2D
{
    // Exported properties for tuning
    [Export] public float EngineForce { get; set; } = 800.0f;
    [Export] public float BrakeForce { get; set; } = 600.0f;
    [Export] public float MaxSpeed { get; set; } = 400.0f;
    [Export] public float SteeringRate { get; set; } = 2.0f; // Maximum steering rate (radians/sec)
    [Export] public float MinSpeedForSteering { get; set; } = 5.0f; // Minimum speed needed for steering
    [Export] public float MaxSteeringSpeed { get; set; } = 200.0f; // Speed at which steering reaches full effectiveness
    [Export] public float MinSteeringEffectiveness { get; set; } = 0.1f; // Minimum steering power at low speeds
    [Export] public float TurnStopDamp { get; set; } = 15.0f; // Damping factor for stopping rotation
    [Export] public float BaseDrag { get; set; } = 1.0f; // Constant drag/friction coefficient
    [Export] public float HandbrakeDragFactor { get; set; } = 4.0f; // Multiplier for drag when handbrake is on
    [Export] public float TractionStrength { get; set; } = 0.85f; // How much to reduce sideways sliding (0-1)

    // Zombie collision properties
    [Export] public float ZombieDamageAmount { get; set; } = 25.0f;
    [Export] public float SlowdownFactorOnHit { get; set; } = 0.8f;
    [Export] public float MinSpeedAfterHit { get; set; } = 2.0f;

    // Tire track properties
    [Export] public Vector2 FrontWheelOffset { get; set; } = new Vector2(0, -30); // Offset from car center to front wheels
    [Export] public Vector2 RearWheelOffset { get; set; } = new Vector2(0, 30); // Offset from car center to rear wheels
    [Export] public float WheelTrackWidth { get; set; } = 24.0f; // Distance between left and right wheels (slightly less than car width)
    [Export] public float TireTrackFadeDuration { get; set; } = 2.0f; // Time for tire tracks to fade out
    [Export] public float MinSpeedForTracks { get; set; } = 5.0f; // Minimum speed to create tire tracks

    // Cached node references
    private AnimatedSprite2D? _sprite;
    private TireTrackSystem? _tireTrackSystem;
    private Area2D? _zombieDetector;
    private bool _isCreatingTireTracks = false;

    public override void _Ready()
    {
        // Cache node references
        _sprite = GetNode<AnimatedSprite2D>("Sprite2D");
        _tireTrackSystem = GetNode<TireTrackSystem>("TireTrackSystem");
        _zombieDetector = GetNode<Area2D>("ZombieDetector");

        // Set initial physics properties
        LinearDamp = BaseDrag;

        // Set collision layer for car (layer 1)
        CollisionLayer = 1; // Binary: 001 (layer 1)
        CollisionMask = 1;  // Can collide with other cars/obstacles

        // Connect zombie detection signal
        if (_zombieDetector != null)
        {
            _zombieDetector.BodyEntered += OnZombieDetectorBodyEntered;
        }
    }

    public override void _PhysicsProcess(double delta)
    {
        HandleInput(delta);
        ApplySpeedLimit();
    }

    private void HandleInput(double delta)
    {
        // Handle acceleration
        if (Input.IsActionPressed("accelerate"))
        {
            Vector2 forwardDirection = Vector2.Up.Rotated(Rotation);
            ApplyCentralForce(forwardDirection * EngineForce);
        }

        // Handle braking
        if (Input.IsActionPressed("brake"))
        {
            Vector2 backwardDirection = Vector2.Down.Rotated(Rotation);
            ApplyCentralForce(backwardDirection * BrakeForce);
        }

        // Handle steering - speed-dependent approach
        float steerInput = Input.GetActionStrength("steer_right") - Input.GetActionStrength("steer_left");
        float currentSpeed = LinearVelocity.Length();

        // Handle tire track creation
        bool shouldCreateTracks = currentSpeed > MinSpeedForTracks && ShouldCreateTireTracks();

        if (shouldCreateTracks && !_isCreatingTireTracks)
        {
            // Start creating tire tracks
            StartTireTracks();
            _isCreatingTireTracks = true;
        }
        else if (shouldCreateTracks && _isCreatingTireTracks)
        {
            // Continue adding to existing tire tracks
            AddToTireTracks();
        }
        else if (!shouldCreateTracks && _isCreatingTireTracks)
        {
            // Stop creating tire tracks
            EndTireTracks();
            _isCreatingTireTracks = false;
        }

        if (steerInput != 0.0f && currentSpeed > MinSpeedForSteering)
        {
            // Calculate steering effectiveness based on speed
            // Linear interpolation from MinSteeringEffectiveness to 1.0 based on speed
            float speedRatio = Mathf.Clamp(currentSpeed / MaxSteeringSpeed, 0.0f, 1.0f);
            float steeringEffectiveness = Mathf.Lerp(MinSteeringEffectiveness, 1.0f, speedRatio);
            float effectiveSteering = steerInput * SteeringRate * steeringEffectiveness;

            AngularVelocity = effectiveSteering;
        }
        else
        {
            // Dampen rotation when no steering input or not moving fast enough
            AngularVelocity = Mathf.Lerp(AngularVelocity, 0f, TurnStopDamp * (float)delta);
        }

        // Apply gentle traction (prevent sliding sideways) - unless handbrake is active
        bool handbrakeActive = Input.IsActionPressed("handbrake");
        if (!handbrakeActive)
        {
            ApplyGentleTraction(delta);
        }

        // Handle handbrake
        if (handbrakeActive)
        {
            LinearDamp = BaseDrag * HandbrakeDragFactor;
        }
        else
        {
            LinearDamp = BaseDrag;
        }
    }

    private void ApplyGentleTraction(double delta)
    {
        // Get the car's forward and right directions
        Vector2 forwardDirection = Vector2.Up.Rotated(Rotation);
        Vector2 rightDirection = Vector2.Right.Rotated(Rotation);

        // Calculate how much the car is moving forward vs sideways
        float forwardVelocity = LinearVelocity.Dot(forwardDirection);
        float sidewaysVelocity = LinearVelocity.Dot(rightDirection);

        // Gradually reduce sideways velocity instead of applying massive forces
        float newSidewaysVelocity = sidewaysVelocity * (1.0f - TractionStrength * (float)delta * 10.0f);

        // Reconstruct velocity with reduced sideways component
        Vector2 newVelocity = forwardDirection * forwardVelocity + rightDirection * newSidewaysVelocity;
        LinearVelocity = newVelocity;
    }

    private void ApplySpeedLimit()
    {
        // Cap speed to MaxSpeed
        if (LinearVelocity.LengthSquared() > MaxSpeed * MaxSpeed)
        {
            LinearVelocity = LinearVelocity.Normalized() * MaxSpeed;
        }
    }

    private Vector2[] GetWheelPositions()
    {
        // Calculate wheel positions in world space
        Vector2 rightDirection = Vector2.Right.Rotated(Rotation);

        // Front wheels
        Vector2 frontCenter = GlobalPosition + FrontWheelOffset.Rotated(Rotation);
        Vector2 frontLeftWheel = frontCenter - rightDirection * (WheelTrackWidth * 0.5f);
        Vector2 frontRightWheel = frontCenter + rightDirection * (WheelTrackWidth * 0.5f);

        // Rear wheels
        Vector2 rearCenter = GlobalPosition + RearWheelOffset.Rotated(Rotation);
        Vector2 rearLeftWheel = rearCenter - rightDirection * (WheelTrackWidth * 0.5f);
        Vector2 rearRightWheel = rearCenter + rightDirection * (WheelTrackWidth * 0.5f);

        return new Vector2[] { frontLeftWheel, frontRightWheel, rearLeftWheel, rearRightWheel };
    }

    private void StartTireTracks()
    {
        if (_tireTrackSystem == null) return;

        Vector2[] wheelPositions = GetWheelPositions();
        _tireTrackSystem.StartTireTrack(wheelPositions, TireTrackFadeDuration);
    }

    private void AddToTireTracks()
    {
        if (_tireTrackSystem == null) return;

        Vector2[] wheelPositions = GetWheelPositions();
        _tireTrackSystem.AddPointsToCurrentTrack(wheelPositions);
    }

    private void EndTireTracks()
    {
        if (_tireTrackSystem == null) return;

        _tireTrackSystem.EndCurrentTireTrack();
    }

    private bool ShouldCreateTireTracks()
    {
        // Check if brake input is pressed
        bool brakePressed = Input.IsActionPressed("brake");
        bool handbrakePressed = Input.IsActionPressed("handbrake");

        // Check if we're applying force against the direction of movement
        Vector2 forwardDirection = Vector2.Up.Rotated(Rotation);
        float forwardVelocity = LinearVelocity.Dot(forwardDirection);

        // Only create tracks if:
        // 1. Brake is pressed AND we're moving forward (braking while going forward)
        // 2. OR if we're accelerating while moving backward (trying to stop/reverse)
        // 3. OR if handbrake is pressed and we're moving (handbrake always creates tracks)
        bool acceleratePressed = Input.IsActionPressed("accelerate");

        bool brakingWhileMovingForward = brakePressed && forwardVelocity > 0;
        bool acceleratingWhileMovingBackward = acceleratePressed && forwardVelocity < 0;
        bool handbraking = handbrakePressed; // Handbrake creates tracks regardless of direction

        return brakingWhileMovingForward || acceleratingWhileMovingBackward || handbraking;
    }

    public override void _ExitTree()
    {
        // Clean up signal connections
        if (_zombieDetector != null)
        {
            _zombieDetector.BodyEntered -= OnZombieDetectorBodyEntered;
        }
    }

    private void OnZombieDetectorBodyEntered(Node2D body)
    {
        // Check if the body is a zombie
        if (body is Zombie zombie)
        {
            // Deal damage to the zombie
            zombie.TakeDamage(ZombieDamageAmount);

            // Apply slowdown to the car
            ApplyZombieHitSlowdown();
        }
    }

    private void ApplyZombieHitSlowdown()
    {
        // Reduce current velocity
        LinearVelocity *= SlowdownFactorOnHit;

        // Ensure car doesn't stop completely if moving forward
        float currentSpeed = LinearVelocity.Length();
        if (currentSpeed < MinSpeedAfterHit && currentSpeed > 0.1f)
        {
            LinearVelocity = LinearVelocity.Normalized() * MinSpeedAfterHit;
        }
    }
}
