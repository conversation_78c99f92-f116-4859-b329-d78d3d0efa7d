using Godot;

namespace CarGame;

public partial class HUD : CanvasLayer
{
    private Label? _killCountLabel;
    
    public override void _Ready()
    {
        // Get reference to kill count label
        _killCountLabel = GetNode<Label>("Control/KillCountLabel");
        
        // Subscribe to kill count changes
        GameState.OnZombiesKilledCountChanged += UpdateKillCounterText;
        
        // Initialize the label with current count
        UpdateKillCounterText(GameState.GetKillCount());
    }
    
    public override void _ExitTree()
    {
        // Unsubscribe to prevent memory leaks
        GameState.OnZombiesKilledCountChanged -= UpdateKillCounterText;
    }
    
    private void UpdateKillCounterText(int newKillCount)
    {
        if (_killCountLabel != null)
        {
            _killCountLabel.Text = $"Kills: {newKillCount}";
        }
    }
}
