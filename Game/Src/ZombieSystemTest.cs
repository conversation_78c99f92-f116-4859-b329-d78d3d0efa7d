using Godot;

namespace CarGame;

/// <summary>
/// Simple test script to verify zombie system functionality
/// </summary>
public partial class ZombieSystemTest : Node
{
    public override void _Ready()
    {
        GD.Print("=== Zombie System Test ===");
        
        // Test GameState
        GD.Print($"Initial kill count: {GameState.GetKillCount()}");
        
        // Test incrementing kills
        GameState.IncrementKills();
        GD.Print($"After increment: {GameState.GetKillCount()}");
        
        // Test zombie creation
        var zombieScene = ResourceLoader.Load<PackedScene>("res://Game/Scenes/Zombie.tscn");
        if (zombieScene != null)
        {
            GD.Print("✓ Zombie scene loaded successfully");
            
            var zombie = zombieScene.Instantiate<Zombie>();
            if (zombie != null)
            {
                GD.Print("✓ Zombie instantiated successfully");
                GD.Print($"  Health: {zombie.Health}/{zombie.MaxHealth}");
                GD.Print($"  Movement Speed: {zombie.MovementSpeed}");
                
                // Test damage
                zombie.TakeDamage(30);
                GD.Print($"  After 30 damage: {zombie.Health}");
                
                zombie.QueueFree();
            }
            else
            {
                GD.Print("✗ Failed to instantiate zombie");
            }
        }
        else
        {
            GD.Print("✗ Failed to load zombie scene");
        }
        
        // Test blood splatter
        var bloodScene = ResourceLoader.Load<PackedScene>("res://Game/Scenes/BloodSplatter.tscn");
        if (bloodScene != null)
        {
            GD.Print("✓ Blood splatter scene loaded successfully");
        }
        else
        {
            GD.Print("✗ Failed to load blood splatter scene");
        }
        
        // Test HUD
        var hudScene = ResourceLoader.Load<PackedScene>("res://Game/Scenes/UI/HUD.tscn");
        if (hudScene != null)
        {
            GD.Print("✓ HUD scene loaded successfully");
        }
        else
        {
            GD.Print("✗ Failed to load HUD scene");
        }
        
        GD.Print("=== Test Complete ===");
    }
}
