using Godot;

namespace CarGame;

public partial class Zombie : CharacterBody2D
{
    // Exported properties
    [Export] public float MaxHealth { get; set; } = 100.0f;
    [Export] public float MovementSpeed { get; set; } = 50.0f;
    
    // Current health
    public float Health { get; private set; }

    // Player tracking
    private Node2D? _player;
    
    // Signal for when zombie dies
    [Signal] public delegate void DiedEventHandler(Vector2 position);
    
    // Required parameterless constructor for Godot
    public Zombie()
    {
    }
    
    public override void _Ready()
    {
        // Initialize health to max
        Health = MaxHealth;

        // Set collision layer and mask for zombies
        // Layer 3 for zombies, don't collide with cars physically (only detected by Area2D)
        CollisionLayer = 4; // Binary: 100 (layer 3)
        CollisionMask = 0;  // Don't collide with anything physically

        // Find the player (car) in the scene
        _player = GetTree().GetFirstNodeInGroup("player") as Node2D;
    }
    
    public override void _PhysicsProcess(double delta)
    {
        // Move towards the player if one exists
        if (_player != null)
        {
            Vector2 direction = (_player.GlobalPosition - GlobalPosition).Normalized();
            Velocity = direction * MovementSpeed;
        }
        else
        {
            // No player found, stop moving
            Velocity = Vector2.Zero;
        }

        // Apply movement
        MoveAndSlide();
    }
    
    /// <summary>
    /// Apply damage to the zombie
    /// </summary>
    /// <param name="amount">Amount of damage to apply</param>
    public void TakeDamage(float amount)
    {
        Health -= amount;
        Health = Mathf.Max(0, Health); // Clamp to 0 minimum
        
        if (Health <= 0)
        {
            Die();
        }
    }
    
    /// <summary>
    /// Handle zombie death
    /// </summary>
    private void Die()
    {
        // Emit death signal with current position
        EmitSignal(SignalName.Died, GlobalPosition);
        
        // Remove zombie from scene
        QueueFree();
    }
}
