using Godot;

namespace CarGame;

public partial class GameManager : Node2D
{
    [Export] public PackedScene? BloodSplatterScene { get; set; }
    [Export] public PackedScene? ZombieScene { get; set; }
    
    // Node for organizing effects
    private Node2D? _effectsContainer;
    
    public override void _Ready()
    {
        // Create effects container
        _effectsContainer = new Node2D();
        _effectsContainer.Name = "Effects";
        AddChild(_effectsContainer);
        
        // Load scenes if not set in editor
        if (BloodSplatterScene == null)
        {
            BloodSplatterScene = ResourceLoader.Load<PackedScene>("res://Game/Scenes/BloodSplatter.tscn");
        }
        
        if (ZombieScene == null)
        {
            ZombieScene = ResourceLoader.Load<PackedScene>("res://Game/Scenes/Zombie.tscn");
        }
    }
    
    /// <summary>
    /// Spawn a zombie at the specified position
    /// </summary>
    /// <param name="position">World position to spawn zombie</param>
    /// <returns>The spawned zombie instance</returns>
    public Zombie? SpawnZombie(Vector2 position)
    {
        if (ZombieScene == null) return null;
        
        var zombieInstance = ZombieScene.Instantiate<Zombie>();
        if (zombieInstance != null)
        {
            zombieInstance.GlobalPosition = position;
            AddChild(zombieInstance);
            
            // Connect to zombie death signal
            zombieInstance.Died += OnZombieDied;
        }
        
        return zombieInstance;
    }
    
    /// <summary>
    /// Handle zombie death - spawn blood splatter and increment kill count
    /// </summary>
    /// <param name="position">Position where zombie died</param>
    private void OnZombieDied(Vector2 position)
    {
        // Spawn blood splatter
        SpawnBloodSplatter(position);
        
        // Increment kill count
        GameState.IncrementKills();
    }
    
    /// <summary>
    /// Spawn blood splatter effect at specified position
    /// </summary>
    /// <param name="position">World position to spawn blood splatter</param>
    private void SpawnBloodSplatter(Vector2 position)
    {
        if (BloodSplatterScene == null || _effectsContainer == null) return;
        
        var splatterInstance = BloodSplatterScene.Instantiate<BloodSplatter>();
        if (splatterInstance != null)
        {
            splatterInstance.GlobalPosition = position;
            _effectsContainer.AddChild(splatterInstance);
        }
    }
    
    /// <summary>
    /// Spawn multiple zombies for testing
    /// </summary>
    /// <param name="count">Number of zombies to spawn</param>
    /// <param name="centerPosition">Center position for spawning</param>
    /// <param name="radius">Radius around center to spawn zombies</param>
    public void SpawnTestZombies(int count, Vector2 centerPosition, float radius = 200.0f)
    {
        for (int i = 0; i < count; i++)
        {
            // Random position around center
            float angle = (float)(GD.Randf() * Mathf.Tau);
            float distance = GD.Randf() * radius;
            Vector2 offset = new Vector2(Mathf.Cos(angle), Mathf.Sin(angle)) * distance;
            Vector2 spawnPosition = centerPosition + offset;
            
            SpawnZombie(spawnPosition);
        }
    }
}
