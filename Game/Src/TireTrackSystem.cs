using Godot;
using System.Collections.Generic;

namespace CarGame;

public partial class TireTrackSystem : Node2D
{
    [Export] public int MaxTrackPoints { get; set; } = 100; // Maximum points per tire track line
    [Export] public float MinDistanceBetweenPoints { get; set; } = 5.0f; // Minimum distance to add new point

    private List<TireTrackLine> _activeTrackLines = new();
    private TireTrackLine? _currentTrackLine = null;

    public override void _Ready()
    {
        // Nothing to initialize
    }
    
    public void StartTireTrack(Vector2[] wheelPositions, float fadeDuration)
    {
        // End current track if one exists
        EndCurrentTireTrack();

        // Create new tire track line
        _currentTrackLine = new TireTrackLine();

        // Add to the level (parent of the car) so it stays in world space
        var level = GetParent().GetParent(); // Car -> Level
        level.AddChild(_currentTrackLine);

        _currentTrackLine.Initialize(wheelPositions, fadeDuration, this);
        _activeTrackLines.Add(_currentTrackLine);

        // Remove oldest track lines if we have too many
        while (_activeTrackLines.Count > 10) // Limit to 10 track lines
        {
            var oldestTrack = _activeTrackLines[0];
            _activeTrackLines.RemoveAt(0);
            oldestTrack?.QueueFree();
        }
    }

    public void AddPointsToCurrentTrack(Vector2[] wheelPositions)
    {
        _currentTrackLine?.AddPoints(wheelPositions);
    }

    public void EndCurrentTireTrack()
    {
        _currentTrackLine?.StopAddingPoints();
        _currentTrackLine = null;
    }

    public void RemoveTrackLine(TireTrackLine trackLine)
    {
        _activeTrackLines.Remove(trackLine);
    }
}

public partial class TireTrackLine : Node2D
{
    private List<Line2D> _wheelLines = new();
    private List<List<float>> _pointAges = new(); // Track the age of each point
    private float _fadeDuration;
    private float _currentTime;
    private Color _originalColor = Colors.Black;
    private TireTrackSystem? _parentSystem;
    private float _minDistanceBetweenPoints = 5.0f;
    private bool _isActive = true; // Whether we're still adding new points

    public void Initialize(Vector2[] wheelPositions, float fadeDuration, TireTrackSystem parentSystem)
    {
        _fadeDuration = fadeDuration;
        _currentTime = 0.0f;
        _parentSystem = parentSystem;
        _minDistanceBetweenPoints = parentSystem.MinDistanceBetweenPoints;

        // Create a Line2D for each wheel
        for (int i = 0; i < wheelPositions.Length; i++)
        {
            var line = new Line2D();
            AddChild(line);

            // Configure line appearance
            line.Width = 2.0f;
            line.DefaultColor = _originalColor;
            line.Antialiased = true;
            line.ZIndex = -10;

            // Convert global position to local position
            Vector2 localPos = ToLocal(wheelPositions[i]);
            line.AddPoint(localPos);

            _wheelLines.Add(line);

            // Initialize point age tracking for this wheel
            var pointAges = new List<float> { 0.0f }; // First point starts at age 0
            _pointAges.Add(pointAges);
        }
    }

    public void AddPoints(Vector2[] wheelPositions)
    {
        if (wheelPositions.Length != _wheelLines.Count || !_isActive) return;

        for (int i = 0; i < wheelPositions.Length; i++)
        {
            var line = _wheelLines[i];
            var pointAges = _pointAges[i];
            Vector2 localPos = ToLocal(wheelPositions[i]);

            // Only add point if it's far enough from the last point
            if (line.GetPointCount() == 0 || line.GetPointPosition(line.GetPointCount() - 1).DistanceTo(localPos) >= _minDistanceBetweenPoints)
            {
                line.AddPoint(localPos);
                pointAges.Add(0.0f); // New point starts at age 0

                // Remove oldest points if we have too many
                if (line.GetPointCount() > _parentSystem?.MaxTrackPoints)
                {
                    line.RemovePoint(0);
                    pointAges.RemoveAt(0);
                }
            }
        }
    }

    public void StopAddingPoints()
    {
        _isActive = false;
    }

    public override void _Process(double delta)
    {
        _currentTime += (float)delta;

        // Update ages of all points
        for (int i = 0; i < _pointAges.Count; i++)
        {
            var pointAges = _pointAges[i];
            for (int j = 0; j < pointAges.Count; j++)
            {
                pointAges[j] += (float)delta;
            }
        }

        // Remove points that are too old and update line colors
        bool hasAnyPoints = false;
        for (int i = 0; i < _wheelLines.Count; i++)
        {
            var line = _wheelLines[i];
            var pointAges = _pointAges[i];

            // Remove points that have exceeded fade duration
            while (pointAges.Count > 0 && pointAges[0] > _fadeDuration)
            {
                line.RemovePoint(0);
                pointAges.RemoveAt(0);
            }

            if (line.GetPointCount() > 0)
            {
                hasAnyPoints = true;
                UpdateLineGradient(line, pointAges);
            }
        }

        // If no points remain, remove this track line
        if (!hasAnyPoints)
        {
            _parentSystem?.RemoveTrackLine(this);
            QueueFree();
        }
    }

    private void UpdateLineGradient(Line2D line, List<float> pointAges)
    {
        if (pointAges.Count == 0) return;

        // For single point, just use default color
        if (pointAges.Count == 1)
        {
            float age = pointAges[0];
            float fadeProgress = age / _fadeDuration;
            float alpha = Mathf.Max(0.0f, 1.0f - fadeProgress);
            line.DefaultColor = new Color(_originalColor.R, _originalColor.G, _originalColor.B, alpha);
            line.Gradient = null;
            return;
        }

        // Create a gradient that fades older points
        var gradient = new Gradient();
        var colors = new Color[pointAges.Count];
        var offsets = new float[pointAges.Count];

        for (int i = 0; i < pointAges.Count; i++)
        {
            float age = pointAges[i];
            float fadeProgress = age / _fadeDuration;
            float alpha = Mathf.Max(0.0f, 1.0f - fadeProgress);

            colors[i] = new Color(_originalColor.R, _originalColor.G, _originalColor.B, alpha);
            offsets[i] = (float)i / (pointAges.Count - 1);
        }

        gradient.Colors = colors;
        gradient.Offsets = offsets;
        line.Gradient = gradient;
    }
}
