using Godot;

namespace CarGame;

public partial class TestLevel : Node2D
{
    private GameManager? _gameManager;
    private Node2D? _car;
    
    public override void _Ready()
    {
        // Get references
        _gameManager = GetNode<GameManager>("GameManager");
        _car = GetNode<Node2D>("Car");
        
        // Spawn some test zombies after a short delay
        GetTree().CreateTimer(1.0).Timeout += SpawnTestZombies;
    }
    
    private void SpawnTestZombies()
    {
        if (_gameManager != null && _car != null)
        {
            // Spawn zombies around the car's starting position
            Vector2 carPosition = _car.GlobalPosition;
            _gameManager.SpawnTestZombies(8, carPosition, 300.0f);
        }
    }
}
