using Godot;
using System;

namespace CarGame;

/// <summary>
/// Global game state singleton for tracking game statistics and state
/// </summary>
public partial class GameState : Node
{
    // Singleton instance
    private static GameState? _instance;
    public static GameState Instance => _instance ?? throw new InvalidOperationException("GameState not initialized");
    
    // Zombie kill tracking
    public static int ZombiesKilledCount { get; private set; } = 0;
    
    // Events for state changes
    public static event Action<int>? OnZombiesKilledCountChanged;
    
    public override void _Ready()
    {
        // Set up singleton
        if (_instance == null)
        {
            _instance = this;
            // Don't queue_free this node when changing scenes
            ProcessMode = ProcessModeEnum.Always;
        }
        else
        {
            // If another instance exists, remove this one
            QueueFree();
        }
    }
    
    /// <summary>
    /// Increment the zombie kill count
    /// </summary>
    public static void IncrementKills()
    {
        ZombiesKilledCount++;
        OnZombiesKilledCountChanged?.Invoke(ZombiesKilledCount);
    }
    
    /// <summary>
    /// Reset the zombie kill count (useful for new games)
    /// </summary>
    public static void ResetKills()
    {
        ZombiesKilledCount = 0;
        OnZombiesKilledCountChanged?.Invoke(ZombiesKilledCount);
    }
    
    /// <summary>
    /// Get current kill count
    /// </summary>
    public static int GetKillCount()
    {
        return ZombiesKilledCount;
    }
}
