using Game.Src.Debug;
using Godot;
using ImGuiGodot;
using ImGuiNET;

namespace Game.Src;

public partial class Main : Node
{
    // Reference to UI nodes
    private Control _mainMenu;
    private Control _settings;
    private SubViewportContainer _gameViewportContainer;

    public Main()
    {
        GameEvents.GameStarted += OnGameStarted;
    }

    public override void _Ready()
    {
        // Get references to UI nodes
        _mainMenu = GetNode<Control>("%MainMenu");
        _settings = GetNode<Control>("%Settings");
        _gameViewportContainer = GetNode<SubViewportContainer>("%GameViewportContainer");
        
        // Initially hide the game viewport
        _gameViewportContainer.Visible = false;
        
        // Connect to UI navigation events
        GameEvents.StartGameRequested += OnStartGameRequested;
        GameEvents.SettingsRequested += OnSettingsRequested;
        GameEvents.MainMenuRequested += OnMainMenuRequested;
        
        GameEvents.EmitGameStarted();
        RegisterDynamicSections();

        // Initialize resolution system with default resolution (1280x720)
        if (IsInstanceValid(ResolutionHelper))
        {
            ResolutionHelper.SetGameContentResolution(new Vector2I(1280, 720));
        }
    }

    public override void _Process(double delta)
    {
        DrawImGuiWindow();
    }
    
    public override void _Input(InputEvent @event)
    {
        // Check for escape key press when game is visible
        if (@event is InputEventKey keyEvent && 
            keyEvent.Pressed && 
            keyEvent.Keycode == Key.Escape && 
            _gameViewportContainer.Visible)
        {
            // Toggle pause menu
            GameEvents.EmitPauseToggled();
        }
    }

    public override void _ExitTree()
    {
        // Disconnect from events
        GameEvents.StartGameRequested -= OnStartGameRequested;
        GameEvents.SettingsRequested -= OnSettingsRequested;
        GameEvents.MainMenuRequested -= OnMainMenuRequested;
        GameEvents.GameStarted -= OnGameStarted;
    }

    private void OnGameStarted()
    {
        Logger.Debug("Game started");
    }
    
    private void OnStartGameRequested()
    {
        // Show game, hide menus
        _gameViewportContainer.Visible = true;
        _mainMenu.Visible = false;
        _settings.Visible = false;
    }
    
    private void OnSettingsRequested()
    {
        // Show settings, hide other UI
        _settings.Visible = true;
        _mainMenu.Visible = false;
    }
    
    private void OnMainMenuRequested()
    {
        // Show main menu, hide other UI
        _mainMenu.Visible = true;
        _settings.Visible = false;
        _gameViewportContainer.Visible = false;
    }
}