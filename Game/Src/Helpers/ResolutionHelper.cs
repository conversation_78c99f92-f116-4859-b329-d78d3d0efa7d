using Godot;
using Game.Src;
using Godot.Collections;

[GlobalClass]
public partial class ResolutionHelper : Node
{
    [Export] public SubViewportContainer ViewportContainer { get; private set; }
    [Export] public SubViewport Viewport { get; private set; }
    [Export] public Node2D GameRootNode { get; private set; }
    [Export] public Array<Vector2I> SupportedResolutions { get; private set; } =
        new()
        {
            new(320, 180),
            new(640, 360),
            new(960, 540),
            new(1280, 720),
            new(1536, 864),
            new(1600, 900),
            new(1920, 1080),
            new(2048, 1152),
            new(2650, 1440),
            new(3840, 2160)
        };
    
    public Vector2I CurrentResolution => Viewport?.Size ?? Vector2I.Zero;
    
    public void SetResolution(Vector2I resolution)
    {
        if (!IsSetupCorrectly()) return;
        
        var baseWindowSize = new Vector2(
            (int)ProjectSettings.GetSetting("display/window/size/viewport_width")
            , (int)ProjectSettings.GetSetting("display/window/size/viewport_height"));
        
        var scale = baseWindowSize / resolution;
        
        var invertedScale = Vector2.One / scale;
        
        ViewportContainer.Size = resolution;
        ViewportContainer.Scale = scale;

        Viewport.Size = resolution;
        GameRootNode.Scale = invertedScale;
        
        Logger.Debug($"baseWindowSize: {baseWindowSize}");
        Logger.Debug($"scale: {scale}");
        Logger.Debug($"invertedScale (for world node): {invertedScale}");
    }

    /// <summary>
    /// Sets the game content resolution for pixel-perfect scaling of game world elements.
    /// This method only affects content rendered within the GameViewport (car, zombies, obstacles)
    /// while preserving the same field of view and object sizes.
    /// </summary>
    /// <param name="resolution">Target resolution for game content rendering</param>
    public void SetGameContentResolution(Vector2I resolution)
    {
        if (!IsSetupCorrectly()) return;

        // Validate resolution maintains 16:9 aspect ratio
        var aspectRatio = (float)resolution.X / resolution.Y;
        var expectedAspectRatio = 16f / 9f;
        if (Mathf.Abs(aspectRatio - expectedAspectRatio) > 0.01f)
        {
            Logger.Warning($"Resolution {resolution} does not maintain 16:9 aspect ratio. Expected: {expectedAspectRatio:F2}, Got: {aspectRatio:F2}");
        }

        // For pixel-perfect scaling with SubViewportContainer stretch=true:
        // We set the ViewportContainer size to the target resolution
        // The container will automatically stretch this to fill the window
        // This creates perfect pixel-perfect scaling without any object transforms
        ViewportContainer.Size = resolution;
        Viewport.Size = resolution;

        var baseWindowSize = new Vector2(1280, 720);
        var pixelScaleX = baseWindowSize.X / resolution.X;
        var pixelScaleY = baseWindowSize.Y / resolution.Y;

        Logger.Debug($"Set game content resolution to: {resolution} (aspect ratio: {aspectRatio:F2})");
        Logger.Debug($"Pixel scale factor: {pixelScaleX:F1}x horizontal, {pixelScaleY:F1}x vertical");
        Logger.Debug($"ViewportContainer size: {ViewportContainer.Size}, Viewport size: {Viewport.Size}");
    }

    private bool IsSetupCorrectly()
    {
        if (!IsInstanceValid(ViewportContainer))
        {
            Logger.Error("ResolutionHelper.ViewportContainer is null");
            return false;
        }
        
        if (!IsInstanceValid(Viewport))
        {
            Logger.Error("ResolutionHelper.Viewport is null");
            return false;
        }
        
        if (!IsInstanceValid(GameRootNode))
        {
            Logger.Error("ResolutionHelper.GameRootNode is null");
            return false;
        }

        return true;
    }
}
